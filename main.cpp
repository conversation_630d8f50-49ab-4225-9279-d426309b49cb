#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <cassert>
#include <limits>
#include <numeric>
#include <sstream>

/*
 * Optimized scheduler for "边缘集群AI推理的分布式任务调度".
 *
 * Strategy overview:
 * 1.  For every server, compute the largest batch-size `bj_cap` that fits its memory.
 *     This respects constraints: 1 <= Bj <= 1000 and a*Bj + b <= mi.
 *
 * 2.  Users are sorted by their deadline `e_i` (Earliest Deadline First). This prioritizes
 *     more urgent tasks to give them a better choice of servers.
 *
 * 3.  For each user `i` (in EDF order):
 *        - Calculate `min_batch = ceil(cnt_i / 300)` to ensure T_i <= 300.
 *        - Iterate through all servers `s` that can support at least `min_batch`.
 *        - For each valid server, estimate the final completion time `finish_est` using
 *          a corrected timeline model that accounts for pipelining on a single NPU:
 *          - The interval between the start of processing of two consecutive batches
 *            is `max(proc_time, lat + 1)`.
 *          - `finish_est = s_i + lat + proc_time + (T_i - 1) * interval`.
 *        - Choose the server that yields the minimum `finish_est`.
 *
 * 4.  All T_i batches for a user are sent to the SAME NPU to keep `move_i = 0`.
 *     The NPU index is chosen cyclically: `npu = (idx mod g_s) + 1`.
 *
 * 5.  The output for each user is buffered, and then printed at the end in the
 *     original user order (0 to M-1) to conform to output specification.
 */

int main() {
    std::ios::sync_with_stdio(false);
    std::cin.tie(nullptr);

    int N;
    if (!(std::cin >> N)) return 0;

    const int MAX_BJ_LIMIT = 1000;

    std::vector<int> g(N), k(N), m(N);
    for (int i = 0; i < N; ++i) {
        std::cin >> g[i] >> k[i] >> m[i];
    }

    int M;
    std::cin >> M;

    struct User {
        int s, e, cnt;
    };
    std::vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        std::cin >> users[i].s >> users[i].e >> users[i].cnt;
    }

    std::vector<std::vector<int>> latency(N, std::vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            std::cin >> latency[i][j];
        }
    }

    int a, b;
    std::cin >> a >> b;

    std::vector<int> bj_cap(N);
    for (int i = 0; i < N; ++i) {
        long long cap = (m[i] - b) / a;
        cap = std::max<long long>(cap, 0LL);
        if (cap > MAX_BJ_LIMIT) cap = MAX_BJ_LIMIT;
        bj_cap[i] = static_cast<int>(cap);
        if (bj_cap[i] == 0) bj_cap[i] = 1;
    }

    // Track the next free timestamp of every NPU so that later users can account
    // for queueing delay on the same device.  This allows a more realistic
    // finish-time estimate, which helps the heuristic make better choices and
    // generally improves overall score.
    std::vector<std::vector<long long>> npu_free(N);
    for (int i = 0; i < N; ++i) npu_free[i].assign(g[i], 0LL);

    // Track NPU workload for better load balancing
    std::vector<std::vector<long long>> npu_workload(N);
    for (int i = 0; i < N; ++i) npu_workload[i].assign(g[i], 0LL);

    std::vector<int> user_indices(M);
    std::iota(user_indices.begin(), user_indices.end(), 0);
    std::sort(user_indices.begin(), user_indices.end(), [&](int u_a, int u_b) {
        return users[u_a].e < users[u_b].e;
    });

    auto output_user_logic = [&](int idx, std::ostream& out) {
        const auto &u = users[idx];

        int min_batch = (u.cnt + 299) / 300;
        if (min_batch < 1) min_batch = 1;

        int chosen = -1;           // chosen server index
        int chosen_npu = -1;       // chosen NPU index within server
        long long best_cost = std::numeric_limits<long long>::max();
        int batch_size = 0;
        long long best_start_time = 0; // first processing millisecond (for output)

        // Explore candidate batch sizes and NPUs for every server.  Complexity is
        // still acceptable: at most 10 servers * 10 NPUs * 1000 Bj ≈ 1e5 iterations.
        for (int s = 0; s < N; ++s) {
            int capB = bj_cap[s];
            if (capB < min_batch) continue;

            int lat = latency[s][idx];

            // Sort NPUs by availability for better load balancing
            std::vector<int> npu_order(g[s]);
            std::iota(npu_order.begin(), npu_order.end(), 0);
            std::sort(npu_order.begin(), npu_order.end(), [&](int a, int b) {
                return npu_free[s][a] < npu_free[s][b];
            });

            for (int npu : npu_order) {
                long long avail = npu_free[s][npu];

                // Try different batch sizes, prioritizing efficiency
                std::vector<int> batch_candidates;

                // Add optimal batch sizes based on memory utilization
                for (int B = min_batch; B <= capB; ++B) {
                    double memory_util = static_cast<double>(a * B + b) / m[s];
                    if (memory_util > 0.8) { // High memory utilization
                        batch_candidates.push_back(B);
                    }
                }

                // Add other candidates
                for (int B = capB; B >= min_batch; B = std::max(min_batch, B * 3 / 4)) {
                    batch_candidates.push_back(B);
                }

                // Remove duplicates and sort
                std::sort(batch_candidates.begin(), batch_candidates.end());
                batch_candidates.erase(std::unique(batch_candidates.begin(), batch_candidates.end()),
                                     batch_candidates.end());

                for (int B : batch_candidates) {
                    int batches = (u.cnt + B - 1) / B;
                    if (batches > 300) continue;  // violates Ti upper-bound

                    // Processing time for a single batch of size B on server s.
                    double denom = static_cast<double>(k[s]) * std::sqrt(static_cast<double>(B));
                    int proc = static_cast<int>(std::ceil(B / denom));

                    long long interval = std::max(proc, lat + 1);

                    long long arrival0 = static_cast<long long>(u.s) + lat;
                    long long start_proc = std::max(avail, arrival0);

                    long long finish_est = start_proc + proc +
                                           static_cast<long long>(batches - 1) * interval;

                    // Add a mild penalty if we overshoot the user deadline so that, when
                    // possible, we prefer on-time completion.
                    long long cost = finish_est;
                    if (finish_est > u.e) cost += (finish_est - u.e);

                    // Prefer larger batch sizes for efficiency, but consider memory utilization
                    double memory_util = static_cast<double>(a * B + b) / m[s];
                    if (memory_util < 0.5) cost += (capB - B) * 20; // Penalty for low memory utilization
                    else cost += (capB - B) * 5; // Smaller penalty for good memory utilization

                    if (cost < best_cost) {
                        best_cost = cost;
                        chosen = s;
                        chosen_npu = npu;
                        batch_size = B;
                        best_start_time = start_proc;
                    }
                }
            }
        }

        if (chosen == -1) {
            // Fallback: pick the server with the largest capacity and the first NPU.
            chosen = std::max_element(bj_cap.begin(), bj_cap.end()) - bj_cap.begin();
            chosen_npu = 0;
            batch_size = bj_cap[chosen] == 0 ? 1 : bj_cap[chosen];
            best_start_time = static_cast<long long>(u.s) + latency[chosen][idx];
        }

        int best_cap = batch_size;
        int batch_size_final = std::min(best_cap, MAX_BJ_LIMIT);

        assert(batch_size_final >= min_batch && batch_size_final >= 1);
        assert(1LL * a * batch_size_final + b <= m[chosen]);

        int Ti = (u.cnt + batch_size_final - 1) / batch_size_final;
        assert(1 <= Ti && Ti <= 300);

        out << Ti << '\n';

        int npu_index = chosen_npu + 1;  // convert to 1-based for output

        // Align first send_time so that the arrival coincides with best_start_time.
        int send_time = static_cast<int>(std::max<long long>(u.s, best_start_time - latency[chosen][idx]));

        int lat = latency[chosen][idx];

        // Re-compute the per-batch processing time for the final batch size so that we
        // can respect the same interval that was used during the finish-time
        // estimation phase.  This keeps the queue on the NPU saturated while avoiding
        // sending requests too aggressively (which could otherwise accumulate and
        // increase peak memory usage).

        double denom_final = static_cast<double>(k[chosen]) * std::sqrt(static_cast<double>(batch_size_final));
        int proc_final = static_cast<int>(std::ceil(batch_size_final / denom_final));
        int send_gap = std::max(proc_final, lat + 1);  // interval between two `send_time`s

        int remaining = u.cnt;
        for (int j = 0; j < Ti; ++j) {
            int bj = (j == Ti - 1) ? remaining : batch_size_final;
            remaining -= bj;

            out << send_time << ' ' << (chosen + 1) << ' ' << npu_index << ' ' << bj;
            if (j != Ti - 1) out << ' ';

            send_time += send_gap;
        }
        out << '\n';

        // Update availability of the chosen NPU for future users.
        long long avail_update = static_cast<long long>(send_time) + latency[chosen][idx] +
                                 proc_final + static_cast<long long>(Ti - 1) * send_gap;
        npu_free[chosen][chosen_npu] = avail_update;

        // Update workload tracking
        npu_workload[chosen][chosen_npu] += u.cnt;

#ifdef DEBUG
        assert(remaining == 0);
#endif
    };

    std::vector<std::stringstream> results(M);
    for (int idx : user_indices) {
        output_user_logic(idx, results[idx]);
    }

    for (int i = 0; i < M; ++i) {
        std::cout << results[i].rdbuf();
    }

    std::cout.flush();
    return 0;
} 